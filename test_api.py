#!/usr/bin/env python3
import requests
import json

# Test StudentBot
print("=== Testing StudentBot ===")
url = "http://localhost:8000/bots/StudentBot/query"
data = {
    "query": "İş sağlığıyla ilgili bilgi verir misin elindeki dökümanları kullan"
}

try:
    response = requests.post(url, json=data)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"Bot Name: {result.get('bot_name')}")
        print(f"Query: {result.get('query')}")
        print(f"Response: {result.get('response')[:200]}...")
        
        tool_responses = result.get('tool_responses', [])
        print(f"Tool Responses Count: {len(tool_responses)}")
        
        for i, tool_resp in enumerate(tool_responses):
            print(f"Tool {i+1}: {tool_resp.get('tool_name')}")
            if tool_resp.get('tool_name') == 'DocumentSearchTool':
                documents = tool_resp.get('response', {}).get('documents', [])
                print(f"  Documents found: {len(documents)}")
                if documents:
                    print(f"  First document preview: {documents[0].get('content', '')[:100]}...")
    else:
        print(f"Error: {response.text}")
        
except Exception as e:
    print(f"Error: {e}")

print("\n" + "="*50 + "\n")

# Test AtlasIQBot
print("=== Testing AtlasIQBot ===")
url2 = "http://localhost:8000/bots/AtlasIQBot/query"
data2 = {
    "query": "RAG sistemi hakkında bilgi verir misin"
}

try:
    response2 = requests.post(url2, json=data2)
    print(f"Status Code: {response2.status_code}")
    
    if response2.status_code == 200:
        result2 = response2.json()
        print(f"Bot Name: {result2.get('bot_name')}")
        print(f"Query: {result2.get('query')}")
        print(f"Response: {result2.get('response')[:200]}...")
        
        tool_responses2 = result2.get('tool_responses', [])
        print(f"Tool Responses Count: {len(tool_responses2)}")
        
        for i, tool_resp in enumerate(tool_responses2):
            print(f"Tool {i+1}: {tool_resp.get('tool_name')}")
            if tool_resp.get('tool_name') == 'DocumentSearchTool':
                documents = tool_resp.get('response', {}).get('documents', [])
                print(f"  Documents found: {len(documents)}")
                if documents:
                    print(f"  First document preview: {documents[0].get('content', '')[:100]}...")
    else:
        print(f"Error: {response2.text}")
        
except Exception as e:
    print(f"Error: {e}")
