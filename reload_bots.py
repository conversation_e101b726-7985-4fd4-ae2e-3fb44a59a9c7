#!/usr/bin/env python3
"""
Reload bots configuration
"""
import asyncio
import aiohttp
import json

async def reload_bots():
    base_url = "http://localhost:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(f"{base_url}/reload") as response:
                if response.status == 200:
                    result = await response.json()
                    print("Bots reloaded successfully:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                else:
                    print(f"Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
    except Exception as e:
        print(f"Request Error: {e}")

if __name__ == "__main__":
    asyncio.run(reload_bots())
