#!/usr/bin/env python3
import sys
sys.path.append('.')
from langchain_chroma import Chroma
from langchain_openai import OpenAIEmbeddings
import os
from dotenv import load_dotenv

load_dotenv()

embeddings = OpenAIEmbeddings(model='text-embedding-3-small')

# Test student_documents
print('=== STUDENT_DOCUMENTS ===')
vector_store = Chroma(
    collection_name='student_documents',
    embedding_function=embeddings,
    persist_directory='./chroma_db/student'
)

collection = vector_store._collection
print(f'Document count: {collection.count()}')

docs = vector_store.similarity_search('iş sağlığı', k=2)
print(f'Search results count: {len(docs)}')
for i, doc in enumerate(docs):
    print(f'Document {i+1}:')
    print(f'  Content preview: {doc.page_content[:150]}...')
    print(f'  Source: {doc.metadata.get("source_file", "Unknown")}')
    print()

print('=== ATLASIQ_DOCUMENTS ===')
vector_store2 = Chroma(
    collection_name='atlasiq_documents',
    embedding_function=embeddings,
    persist_directory='./chroma_db/atlasiq'
)

collection2 = vector_store2._collection
print(f'Document count: {collection2.count()}')

docs2 = vector_store2.similarity_search('RAG system', k=2)
print(f'Search results count: {len(docs2)}')
for i, doc in enumerate(docs2):
    print(f'Document {i+1}:')
    print(f'  Content preview: {doc.page_content[:150]}...')
    print(f'  Source: {doc.metadata.get("source_file", "Unknown")}')
    print()
