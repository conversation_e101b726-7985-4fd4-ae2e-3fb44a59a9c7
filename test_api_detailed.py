#!/usr/bin/env python3
"""
Detailed API test to debug document search issues
"""
import asyncio
import aiohttp
import json


async def test_api():
    base_url = "http://localhost:8000"

    # Test StudentBot
    print("=== Testing StudentBot ===")
    student_payload = {
        "query": "iş sağlığı ve güvenliği hakkında bilgi ver",
        "metadata": {},
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}/bots/StudentBot/query",
                json=student_payload,
                headers={"Content-Type": "application/json"},
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"StudentBot Response Status: {response.status}")
                    print(
                        f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}"
                    )
                else:
                    print(f"StudentBot Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
    except Exception as e:
        print(f"StudentBot Request Error: {e}")

    print("\n" + "=" * 60 + "\n")

    # Test AtlasIQBot
    print("=== Testing AtlasIQBot ===")
    atlasiq_payload = {
        "query": "RAG sistemi nasıl çalışır, dökümanlardan bilgi ver",
        "metadata": {},
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}/bots/AtlasIQBot/query",
                json=atlasiq_payload,
                headers={"Content-Type": "application/json"},
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"AtlasIQBot Response Status: {response.status}")
                    print(
                        f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}"
                    )
                else:
                    print(f"AtlasIQBot Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
    except Exception as e:
        print(f"AtlasIQBot Request Error: {e}")

    print("\n" + "=" * 60 + "\n")

    # Test with more explicit document search keywords
    print("=== Testing StudentBot with explicit document keywords ===")
    student_payload2 = {
        "query": "document search iş güvenliği pdf dosyalarından bilgi getir",
        "metadata": {},
    }

    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{base_url}/bots/StudentBot/query",
                json=student_payload2,
                headers={"Content-Type": "application/json"},
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"StudentBot Response Status: {response.status}")
                    print(
                        f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}"
                    )
                else:
                    print(f"StudentBot Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
    except Exception as e:
        print(f"StudentBot Request Error: {e}")


if __name__ == "__main__":
    asyncio.run(test_api())
