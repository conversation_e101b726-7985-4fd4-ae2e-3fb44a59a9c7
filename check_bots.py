#!/usr/bin/env python3
"""
Check available bots
"""
import asyncio
import aiohttp
import json

async def check_bots():
    base_url = "http://localhost:8000"
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(f"{base_url}/bots") as response:
                if response.status == 200:
                    result = await response.json()
                    print("Available bots:")
                    print(json.dumps(result, indent=2, ensure_ascii=False))
                    
                    # Extract bot names
                    bot_names = [bot['name'] for bot in result['bots']]
                    print(f"\nBot names: {bot_names}")
                else:
                    print(f"Error: {response.status}")
                    error_text = await response.text()
                    print(f"Error details: {error_text}")
    except Exception as e:
        print(f"Request Error: {e}")

if __name__ == "__main__":
    asyncio.run(check_bots())
