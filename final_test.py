#!/usr/bin/env python3
"""
Final test to verify document search functionality
"""
import asyncio
import aiohttp
import json

async def final_test():
    base_url = "http://localhost:8000"
    
    # Test cases that should use DocumentSearchTool
    test_cases = [
        {
            "name": "StudentBot - Turkish occupational safety query",
            "bot": "StudentBot",
            "query": "iş güvenliği hakkında dökümanlardan bilgi ver",
            "expected_tool": "DocumentSearchTool"
        },
        {
            "name": "StudentBot - Turkish document search query",
            "bot": "StudentBot", 
            "query": "belgelerde iş sağlığı konusunda ne yazıyor",
            "expected_tool": "DocumentSearchTool"
        },
        {
            "name": "AtlasIQBot - RAG system query",
            "bot": "AtlasIQBot",
            "query": "RAG sistemi dökümanlarında nasıl açıklanmış",
            "expected_tool": "DocumentSearchTool"
        },
        {
            "name": "AtlasIQBot - MyForm project query",
            "bot": "AtlasIQBot",
            "query": "MyForm projesi hakkında dökümanlardan bilgi getir",
            "expected_tool": "DocumentSearchTool"
        }
    ]
    
    results = []
    
    for test_case in test_cases:
        print(f"\n=== {test_case['name']} ===")
        
        payload = {
            "query": test_case["query"],
            "metadata": {}
        }
        
        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    f"{base_url}/bots/{test_case['bot']}/query",
                    json=payload,
                    headers={"Content-Type": "application/json"}
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        selected_tools = result.get("selected_tools", [])
                        tool_responses = result.get("tool_responses", [])
                        
                        # Check if expected tool was selected
                        tool_selected = test_case["expected_tool"] in selected_tools
                        
                        # Check if tool returned documents
                        documents_found = False
                        document_count = 0
                        for tool_response in tool_responses:
                            if tool_response.get("tool_name") == test_case["expected_tool"]:
                                content = tool_response.get("content", {})
                                if content.get("success") and content.get("documents"):
                                    documents_found = True
                                    document_count = len(content.get("documents", []))
                                    break
                        
                        test_result = {
                            "test_name": test_case["name"],
                            "query": test_case["query"],
                            "expected_tool": test_case["expected_tool"],
                            "selected_tools": selected_tools,
                            "tool_selected": tool_selected,
                            "documents_found": documents_found,
                            "document_count": document_count,
                            "success": tool_selected and documents_found
                        }
                        
                        results.append(test_result)
                        
                        print(f"✅ Tool Selected: {tool_selected} ({selected_tools})")
                        print(f"✅ Documents Found: {documents_found} ({document_count} docs)")
                        print(f"✅ Overall Success: {test_result['success']}")
                        
                        if test_result['success']:
                            print(f"📄 Response Preview: {result.get('response', '')[:200]}...")
                        
                    else:
                        print(f"❌ HTTP Error: {response.status}")
                        error_text = await response.text()
                        print(f"Error details: {error_text}")
                        
                        results.append({
                            "test_name": test_case["name"],
                            "query": test_case["query"],
                            "success": False,
                            "error": f"HTTP {response.status}: {error_text}"
                        })
                        
        except Exception as e:
            print(f"❌ Request Error: {e}")
            results.append({
                "test_name": test_case["name"],
                "query": test_case["query"],
                "success": False,
                "error": str(e)
            })
    
    # Summary
    print("\n" + "="*60)
    print("FINAL TEST SUMMARY")
    print("="*60)
    
    successful_tests = [r for r in results if r.get("success", False)]
    failed_tests = [r for r in results if not r.get("success", False)]
    
    print(f"✅ Successful Tests: {len(successful_tests)}/{len(results)}")
    print(f"❌ Failed Tests: {len(failed_tests)}/{len(results)}")
    
    if successful_tests:
        print("\n✅ SUCCESSFUL TESTS:")
        for test in successful_tests:
            print(f"  - {test['test_name']}: {test['document_count']} documents found")
    
    if failed_tests:
        print("\n❌ FAILED TESTS:")
        for test in failed_tests:
            print(f"  - {test['test_name']}: {test.get('error', 'Unknown error')}")
    
    # Overall result
    if len(successful_tests) == len(results):
        print(f"\n🎉 ALL TESTS PASSED! Document search functionality is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Document search functionality needs attention.")

if __name__ == "__main__":
    asyncio.run(final_test())
