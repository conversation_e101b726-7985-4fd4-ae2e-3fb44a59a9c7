#!/usr/bin/env python3
"""
Debug script to test DocumentSearchTool directly
"""
import asyncio
import os
from dotenv import load_dotenv
from app.tools.document_search import DocumentSearchTool

# Load environment variables
load_dotenv()


async def test_document_search():
    print("=== Testing DocumentSearchTool Directly ===")

    # Test StudentBot configuration
    print("\n--- Testing StudentBot Configuration ---")
    student_config = {
        "collection_name": "student_documents",
        "persist_directory": "./chroma_db/student",
        "top_k": 5,
    }

    try:
        student_tool = DocumentSearchTool(student_config)
        print(f"StudentBot DocumentSearchTool initialized successfully")
        print(f"Collection: {student_tool.collection_name}")
        print(f"Persist directory: {student_tool.persist_directory}")

        # Test search
        query = "iş sağlığı"
        print(f"\nSearching for: '{query}'")
        result = await student_tool.execute(query)
        print(f"Result: {result}")

        if result.get("success"):
            documents = result.get("documents", [])
            print(f"Found {len(documents)} documents")
            for i, doc in enumerate(documents[:2]):  # Show first 2
                print(f"Document {i+1}: {doc.get('content', '')[:100]}...")
        else:
            print(f"Search failed: {result.get('error')}")

    except Exception as e:
        print(f"Error with StudentBot DocumentSearchTool: {e}")
        import traceback

        traceback.print_exc()

    print("\n" + "=" * 50)

    # Test AtlasIQBot configuration
    print("\n--- Testing AtlasIQBot Configuration ---")
    atlasiq_config = {
        "collection_name": "atlasiq_documents",
        "persist_directory": "./chroma_db/atlasiq",
        "top_k": 8,
        "embedding_model": "text-embedding-3-small",
    }

    try:
        atlasiq_tool = DocumentSearchTool(atlasiq_config)
        print(f"AtlasIQBot DocumentSearchTool initialized successfully")
        print(f"Collection: {atlasiq_tool.collection_name}")
        print(f"Persist directory: {atlasiq_tool.persist_directory}")

        # Test search
        query = "RAG system"
        print(f"\nSearching for: '{query}'")
        result = await atlasiq_tool.execute(query)
        print(f"Result: {result}")

        if result.get("success"):
            documents = result.get("documents", [])
            print(f"Found {len(documents)} documents")
            for i, doc in enumerate(documents[:2]):  # Show first 2
                print(f"Document {i+1}: {doc.get('content', '')[:100]}...")
        else:
            print(f"Search failed: {result.get('error')}")

    except Exception as e:
        print(f"Error with AtlasIQBot DocumentSearchTool: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(test_document_search())
